@echo off
REM Confluence MCP Server启动脚本

echo ========================================
echo Confluence MCP Server 启动
echo ========================================
echo.

REM 检查JAR文件是否存在
if not exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo 错误: JAR文件不存在
    echo 请先运行构建: simple-rebuild.bat
    pause
    exit /b 1
)

REM 设置Confluence连接信息（请根据实际情况修改）
set CONFLUENCE_USERNAME=%CONFLUENCE_USERNAME%
set CONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD%

if "%CONFLUENCE_USERNAME%"=="" (
    echo 警告: CONFLUENCE_USERNAME 环境变量未设置
    echo 请设置您的Confluence用户名和密码
    echo.
    set /p CONFLUENCE_USERNAME="请输入Confluence用户名: "
    set /p CONFLUENCE_PASSWORD="请输入Confluence密码或API Token: "
)

echo 启动配置:
echo - 用户名: %CONFLUENCE_USERNAME%
echo - 服务器: https://doc.greatld.com
echo - 传输模式: STDIO
echo.

echo 正在启动MCP服务器...
echo 按 Ctrl+C 停止服务器
echo.

REM 启动MCP服务器（STDIO模式）
java -Dspring.ai.mcp.server.transport=STDIO ^
     -Dspring.main.web-application-type=none ^
     -Dlogging.pattern.console= ^
     -DCONFLUENCE_USERNAME=%CONFLUENCE_USERNAME% ^
     -DCONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD% ^
     -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar

echo.
echo MCP服务器已停止
pause
