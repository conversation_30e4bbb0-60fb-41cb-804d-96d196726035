@echo off
REM 设置Confluence环境变量脚本

echo ========================================
echo Confluence 环境变量设置
echo ========================================
echo.

echo 此脚本将帮助您设置Confluence认证的环境变量
echo 这样您就不需要在配置文件中硬编码密码
echo.

set /p CONFLUENCE_USERNAME="请输入Confluence用户名: "
set /p CONFLUENCE_PASSWORD="请输入Confluence密码或API Token: "

echo.
echo 正在设置环境变量...

REM 设置当前会话的环境变量
set CONFLUENCE_USERNAME=%CONFLUENCE_USERNAME%
set CONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD%

echo 当前会话环境变量已设置 ✓
echo.

REM 询问是否要永久设置
echo 是否要永久设置这些环境变量? (y/n)
echo 注意: 这将在系统中永久保存您的认证信息
set /p SET_PERMANENT=

if /i "%SET_PERMANENT%"=="y" (
    echo.
    echo 正在设置永久环境变量...
    
    REM 设置用户环境变量
    setx CONFLUENCE_USERNAME "%CONFLUENCE_USERNAME%"
    setx CONFLUENCE_PASSWORD "%CONFLUENCE_PASSWORD%"
    
    echo 永久环境变量已设置 ✓
    echo 请重启命令行窗口以使永久设置生效
) else (
    echo 仅设置了当前会话的环境变量
)

echo.
echo 现在您可以使用以下配置（不包含明文密码）:
echo.

REM 生成安全的配置文件
(
echo {
echo   "mcpServers": {
echo     "qccConfluenceMcp": {
echo       "command": "java",
echo       "args": [
echo         "-Dspring.ai.mcp.server.transport=STDIO",
echo         "-Dspring.main.web-application-type=none",
echo         "-Dlogging.pattern.console=",
echo         "-jar",
echo         "%CD:\=\\%\\target\\qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
echo       ],
echo       "env": {
echo         "CONFLUENCE_USERNAME": "%%CONFLUENCE_USERNAME%%",
echo         "CONFLUENCE_PASSWORD": "%%CONFLUENCE_PASSWORD%%"
echo       }
echo     }
echo   }
echo }
) > augment-mcp-config-secure.json

type augment-mcp-config-secure.json

echo.
echo 安全配置文件已保存为: augment-mcp-config-secure.json
echo 此配置使用环境变量，不包含明文密码

pause
