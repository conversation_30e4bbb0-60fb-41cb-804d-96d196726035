@echo off
chcp 65001 >nul
REM Simple build script

echo Building Confluence MCP Project...
echo.

REM Check if Java is available
java -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java not found
    echo Please install Java 17 or higher
    pause
    exit /b 1
)

echo Java version:
java -version
echo.

REM Try to set JAVA_HOME if not set
if "%JAVA_HOME%"=="" (
    echo JAVA_HOME not set, trying to detect...
    
    if exist "C:\Program Files\Java\jdk-17" (
        set JAVA_HOME=C:\Program Files\Java\jdk-17
        echo Found Java at: C:\Program Files\Java\jdk-17
    ) else if exist "C:\Program Files\Java\jdk-21" (
        set JAVA_HOME=C:\Program Files\Java\jdk-21
        echo Found Java at: C:\Program Files\Java\jdk-21
    ) else if exist "C:\Program Files\OpenJDK\jdk-17" (
        set JAVA_HOME=C:\Program Files\OpenJDK\jdk-17
        echo Found Java at: C:\Program Files\OpenJDK\jdk-17
    ) else (
        echo Could not detect JAVA_HOME automatically
        echo Please set JAVA_HOME manually
        pause
        exit /b 1
    )
)

echo JAVA_HOME: %JAVA_HOME%
echo.

REM Check pom.xml
if not exist "pom.xml" (
    echo ERROR: pom.xml not found
    pause
    exit /b 1
)

echo Building with Maven Wrapper...
echo.

REM Build the project
call mvnw.cmd clean package -DskipTests

if %ERRORLEVEL% neq 0 (
    echo.
    echo BUILD FAILED!
    echo.
    echo Common solutions:
    echo 1. Check internet connection for Maven dependencies
    echo 2. Ensure Java 17+ is installed
    echo 3. Try: mvnw.cmd dependency:resolve
    echo.
    pause
    exit /b 1
)

echo.
echo BUILD SUCCESS!
echo.

if exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo JAR file created: target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
    echo.
    echo You can now:
    echo 1. Run MCP server: start-mcp-server.bat
    echo 2. Setup multiple instances: quick-multi-setup.bat
    echo 3. Test connection: test-mcp-client.bat
) else (
    echo WARNING: JAR file not found
)

echo.
pause
