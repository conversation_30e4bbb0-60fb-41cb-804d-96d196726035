# Augment MCP 客户端配置指南

本指南将帮助您在Augment中配置Confluence MCP服务器，以便在AI对话中访问Confluence文档。

## 配置步骤

### 1. 获取Confluence认证信息

#### 方法A：使用用户名和密码
- **用户名**: 您的Confluence登录用户名
- **密码**: 您的Confluence登录密码

#### 方法B：使用API Token（推荐）
1. 登录到您的Confluence实例
2. 进入 **个人设置** → **安全** → **API令牌**
3. 创建新的API令牌
4. 复制生成的令牌作为密码使用

### 2. 配置Augment

#### 选项1：使用提供的配置文件

将 `augment-mcp-config.json` 文件内容复制到Augment的MCP配置中：

```json
{
  "mcpServers": {
    "qccConfluenceMcp": {
      "command": "java",
      "args": [
        "-Dspring.ai.mcp.server.transport=STDIO",
        "-Dspring.main.web-application-type=none",
        "-Dlogging.pattern.console=",
        "-jar",
        "C:/Users/<USER>/Desktop/idea/qccConfluenceMcp/target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
      ],
      "env": {
        "CONFLUENCE_USERNAME": "你的Confluence用户名",
        "CONFLUENCE_PASSWORD": "你的Confluence密码或API_Token"
      }
    }
  }
}
```

#### 选项2：手动配置

1. 打开Augment的MCP服务器配置
2. 添加新的MCP服务器：
   - **名称**: `qccConfluenceMcp`
   - **命令**: `java`
   - **参数**: 
     ```
     -Dspring.ai.mcp.server.transport=STDIO
     -Dspring.main.web-application-type=none
     -Dlogging.pattern.console=
     -jar
     C:/Users/<USER>/Desktop/idea/qccConfluenceMcp/target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar
     ```
   - **环境变量**:
     - `CONFLUENCE_USERNAME`: 你的用户名
     - `CONFLUENCE_PASSWORD`: 你的密码或API Token

### 3. 修改配置中的认证信息

**重要**: 请将配置文件中的占位符替换为实际的认证信息：

```json
"env": {
  "CONFLUENCE_USERNAME": "实际的用户名",
  "CONFLUENCE_PASSWORD": "实际的密码或API_Token"
}
```

### 4. 验证配置

配置完成后，您可以在Augment中使用以下功能：

#### 可用工具：

1. **searchPages_qccConfluenceMcp**: 搜索Confluence页面
   ```
   搜索关键词"Spring Boot"相关的文档
   ```

2. **getPageContent_qccConfluenceMcp**: 获取页面内容
   ```
   获取页面ID为"123456"的详细内容
   ```

3. **getSpaceInfo_qccConfluenceMcp**: 获取空间信息
   ```
   获取空间"DEMO"的信息
   ```

4. **getSpacePages_qccConfluenceMcp**: 获取空间页面列表
   ```
   获取空间"DEMO"下的所有页面
   ```

## 故障排除

### 常见问题

1. **认证失败**
   - 检查用户名和密码是否正确
   - 确认账户有访问Confluence的权限
   - 如果使用API Token，确保令牌有效

2. **连接超时**
   - 检查网络连接
   - 确认Confluence服务器地址正确
   - 检查防火墙设置

3. **Java路径问题**
   - 确保系统已安装Java 17或更高版本
   - 确认Java在系统PATH中
   - 可以使用完整的Java路径：`C:/Program Files/Java/jdk-17/bin/java`

4. **JAR文件路径问题**
   - 确认JAR文件路径正确
   - 使用绝对路径避免相对路径问题
   - 确保JAR文件已构建：`mvn clean install -DskipTests`

### 调试步骤

1. **检查JAR文件是否存在**:
   ```bash
   dir target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
   ```

2. **手动测试MCP服务器**:
   ```bash
   start-mcp-server-debug.bat
   ```

3. **查看日志文件**:
   ```
   target/confluence-mcp-server.log
   ```

## 安全建议

1. **不要在配置文件中硬编码密码**
2. **使用API Token而不是密码**
3. **定期更新API Token**
4. **限制API Token的权限范围**

## 示例使用

配置完成后，您可以在Augment中这样使用：

```
请帮我搜索Confluence中关于"API文档"的页面，并总结其中的主要内容。
```

Augment将自动调用Confluence MCP服务器来搜索和获取相关文档内容。
