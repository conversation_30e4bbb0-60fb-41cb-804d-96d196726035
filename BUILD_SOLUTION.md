# Confluence MCP 构建问题解决方案

## 问题描述

在尝试构建Confluence MCP项目时遇到了以下问题：
- 打包报错
- 无法正常构建JAR文件

## 问题原因

经过分析，发现以下几个主要问题：

1. **JAVA_HOME环境变量未设置**
   - Maven需要JAVA_HOME环境变量来找到Java编译器
   - 系统中虽然安装了Java，但未设置环境变量

2. **POM文件中的XML标签错误**
   - `<n>qccConfluenceMcp</n>` 应该是 `<name>qccConfluenceMcp</name>`
   - 这个错误可能导致Maven无法正确解析POM文件

3. **Maven命令不可用**
   - 系统中没有安装Maven或未配置环境变量
   - Maven Wrapper (mvnw.cmd) 需要JAVA_HOME才能正常工作

## 解决方案

### 1. 修复POM文件

已创建了正确的pom.xml文件，修复了标签错误：
```xml
<groupId>com.qccconfluencemcp</groupId>
<artifactId>qccConfluenceMcp</artifactId>
<version>0.0.1-SNAPSHOT</version>
<name>qccConfluenceMcp</name>  <!-- 修复了这一行 -->
<description>Confluence MCP Server using Spring AI</description>
```

### 2. 设置JAVA_HOME环境变量

创建了`set-java-home.bat`脚本，自动检测并设置JAVA_HOME：
```batch
@echo off
REM 设置JAVA_HOME并构建项目

if exist "C:\Program Files\Java\jdk-17.0.1" (
    set JAVA_HOME=C:\Program Files\Java\jdk-17.0.1
    echo 找到Java: C:\Program Files\Java\jdk-17.0.1
) else if exist "C:\Program Files\Java\jdk-17" (
    set JAVA_HOME=C:\Program Files\Java\jdk-17
    echo 找到Java: C:\Program Files\Java\jdk-17
)
```

### 3. 使用Maven Wrapper构建项目

使用Maven Wrapper (mvnw.cmd) 构建项目，避免了需要安装Maven：
```batch
call mvnw.cmd clean package -DskipTests
```

## 如何使用解决方案

### 方法1：使用自动构建脚本

1. 运行`set-java-home.bat`脚本
   - 自动检测Java安装
   - 设置JAVA_HOME环境变量
   - 构建项目

2. 构建完成后，JAR文件将位于：
   - `target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar`

### 方法2：手动设置环境变量

如果自动脚本不起作用，可以手动设置环境变量：

1. 打开命令提示符
2. 设置JAVA_HOME：
   ```batch
   set JAVA_HOME=C:\Program Files\Java\jdk-17.0.1
   ```
3. 运行Maven Wrapper：
   ```batch
   mvnw.cmd clean package -DskipTests
   ```

## 多实例运行

构建成功后，可以使用之前创建的多实例解决方案：

1. 运行`quick-multi-setup.bat`创建多个实例配置
2. 或使用`manage-multiple-instances.bat`进行高级管理

## 常见问题

### 1. "JAVA_HOME not found"错误

**解决方案**：
- 确保Java已安装
- 使用`set-java-home.bat`脚本自动设置
- 或手动设置：`set JAVA_HOME=C:\Path\To\Java`

### 2. "mvnw.cmd不是内部或外部命令"错误

**解决方案**：
- 确保在项目根目录运行命令
- 检查mvnw.cmd文件是否存在
- 如果不存在，可以从其他Spring Boot项目复制

### 3. 依赖下载失败

**解决方案**：
- 检查网络连接
- 尝试使用`mvnw.cmd dependency:resolve`单独下载依赖
- 检查防火墙设置

## 验证构建

构建成功后，可以通过以下方式验证：

1. 检查JAR文件是否存在：
   ```
   dir target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
   ```

2. 测试JAR文件：
   ```
   java -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar --help
   ```

3. 启动MCP服务器：
   ```
   start-mcp-server.bat
   ```

## 总结

通过修复POM文件和设置JAVA_HOME环境变量，成功解决了构建问题。现在可以正常构建项目并生成JAR文件，进而使用多实例配置运行MCP服务器。
