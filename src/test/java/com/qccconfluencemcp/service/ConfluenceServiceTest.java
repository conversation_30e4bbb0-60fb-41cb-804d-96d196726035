package com.qccconfluencemcp.service;

import com.qccconfluencemcp.ConfluenceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Confluence服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
class ConfluenceServiceTest {

    @Autowired
    private ConfluenceService confluenceService;

    @BeforeEach
    void setUp() {
        // 测试前的准备工作
    }

    @Test
    void testSearchPages() {

    }

    @Test
    void testGetPageContent() {

    }

    @Test
    void testGetSpaceInfo() {

    }

    @Test
    void testGetSpacePages() {

    }
}
