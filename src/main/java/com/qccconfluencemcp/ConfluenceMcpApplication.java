package com.qccconfluencemcp;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

/**
 * Confluence MCP服务器主应用类
 * <p>
 * 基于Spring AI MCP Server Boot Starter构建的Confluence集成服务器
 * 提供Confluence文档搜索、获取、创建和更新等功能
 * <p>
 * 支持多实例运行，通过环境变量区分不同实例
 */
@SpringBootApplication
public class ConfluenceMcpApplication {

    private static final Logger logger = LoggerFactory.getLogger(ConfluenceMcpApplication.class);

    @Value("${MCP_SERVER_NAME:confluence-mcp-server}")
    private String serverName;

    @Value("${MCP_INSTANCE_ID:default}")
    private String instanceId;

    public static void main(String[] args) {
        // 设置系统属性以支持多实例
        System.setProperty("spring.application.name",
                System.getProperty("MCP_SERVER_NAME", "confluence-mcp-server"));

        // 禁用JMX以避免多实例冲突
        System.setProperty("spring.jmx.enabled", "false");

        // 设置随机端口
        if (System.getProperty("server.port") == null) {
            System.setProperty("server.port", "0");
        }

        logger.info("启动Confluence MCP服务器实例: {}",
                System.getProperty("MCP_SERVER_NAME", "confluence-mcp-server"));
        logger.info("实例ID: {}",
                System.getProperty("MCP_INSTANCE_ID", "default"));

        SpringApplication.run(ConfluenceMcpApplication.class, args);
    }

    /**
     * 注册Confluence工具到MCP服务器
     *
     * @param confluenceService Confluence服务实例
     * @return ToolCallbackProvider 工具回调提供者
     */
    @Bean
    public ToolCallbackProvider confluenceTools(ConfluenceService confluenceService) {
        logger.info("注册Confluence工具到MCP服务器实例: {} (ID: {})", serverName, instanceId);
        return MethodToolCallbackProvider.builder()
                .toolObjects(confluenceService)
                .build();
    }
}
