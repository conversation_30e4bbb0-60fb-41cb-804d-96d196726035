# Confluence MCP服务器配置
spring:
  application:
    name: ${MCP_SERVER_NAME:confluence-mcp-server}

  # Web应用配置
  main:
    banner-mode: off
    web-application-type: none

  # JMX配置（避免多实例冲突）
  jmx:
    enabled: false

  # Spring AI MCP服务器配置
  ai:
    mcp:
      server:
        enabled: true
        name: ${MCP_SERVER_NAME:confluence-mcp-server}
        version: 1.0.0
        type: SYNC
        # 变更通知
        resource-change-notification: true
        tool-change-notification: true
        prompt-change-notification: true
        # 添加实例ID以避免冲突
        instance-id: ${MCP_INSTANCE_ID:${random.uuid}}

# 多实例支持配置
server:
  port: 0  # 随机端口，避免端口冲突

# Confluence连接配置
confluence:
  # Confluence服务器地址
  base-url: https://doc.greatld.com

  # 认证信息（请在环境变量或外部配置文件中设置）
  username: ${CONFLUENCE_USERNAME:}
  password: ${CONFLUENCE_PASSWORD:}

  # 连接超时配置
  connect-timeout: 30000
  read-timeout: 60000

  # SSL验证
  ssl-verification: true

# 日志配置
logging:
  level:
    com.qccconfluencemcp: INFO
    org.springframework.ai: INFO
    org.springframework.ai.mcp: DEBUG
  # STDIO模式需要清空控制台日志格式（生产环境）
  pattern:
    console: ""
  file:
    name: ./target/confluence-mcp-server-${MCP_INSTANCE_ID:default}.log
