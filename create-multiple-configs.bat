@echo off
REM 创建多个MCP配置的脚本
REM 解决同一个MCP包只能运行一次配置的问题

echo ========================================
echo 创建多个MCP配置
echo ========================================
echo.

echo 此脚本将为您创建多个独立的MCP配置，解决同一包只能运行一次的问题
echo.

REM 获取基本信息
set /p PROJECT_COUNT="请输入需要创建的项目配置数量: "
set /p BASE_NAME="请输入配置基础名称 (如: confluence-mcp): "

echo.
echo 正在创建 %PROJECT_COUNT% 个配置...
echo.

REM 创建配置目录
if not exist "configs" mkdir configs

REM 循环创建配置
for /L %%i in (1,1,%PROJECT_COUNT%) do (
    echo 创建配置 %%i...
    
    REM 创建独立的配置文件
    (
        echo {
        echo   "mcpServers": {
        echo     "%BASE_NAME%-%%i": {
        echo       "command": "java",
        echo       "args": [
        echo         "-Dspring.ai.mcp.server.transport=STDIO",
        echo         "-Dspring.main.web-application-type=none",
        echo         "-Dlogging.pattern.console=",
        echo         "-DMCP_SERVER_NAME=%BASE_NAME%-%%i",
        echo         "-DMCP_INSTANCE_ID=%BASE_NAME%-instance-%%i",
        echo         "-Dserver.port=0",
        echo         "-Dspring.jmx.enabled=false",
        echo         "-jar",
        echo         "%CD:\=\\%\\target\\qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
        echo       ],
        echo       "env": {
        echo         "CONFLUENCE_USERNAME": "%%CONFLUENCE_USERNAME%%",
        echo         "CONFLUENCE_PASSWORD": "%%CONFLUENCE_PASSWORD%%"
        echo       }
        echo     }
        echo   }
        echo }
    ) > "configs\%BASE_NAME%-config-%%i.json"
    
    REM 创建独立的启动脚本
    (
        echo @echo off
        echo REM %BASE_NAME% MCP Server %%i 启动脚本
        echo.
        echo REM 设置Confluence连接信息
        echo set CONFLUENCE_USERNAME=%%CONFLUENCE_USERNAME%%
        echo set CONFLUENCE_PASSWORD=%%CONFLUENCE_PASSWORD%%
        echo.
        echo REM 启动MCP服务器实例 %%i
        echo java -Dspring.ai.mcp.server.transport=STDIO ^^^
        echo      -Dspring.main.web-application-type=none ^^^
        echo      -Dlogging.pattern.console= ^^^
        echo      -DMCP_SERVER_NAME=%BASE_NAME%-%%i ^^^
        echo      -DMCP_INSTANCE_ID=%BASE_NAME%-instance-%%i ^^^
        echo      -Dserver.port=0 ^^^
        echo      -Dspring.jmx.enabled=false ^^^
        echo      -DCONFLUENCE_USERNAME=%%CONFLUENCE_USERNAME%% ^^^
        echo      -DCONFLUENCE_PASSWORD=%%CONFLUENCE_PASSWORD%% ^^^
        echo      -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
        echo.
        echo pause
    ) > "configs\start-%BASE_NAME%-%%i.bat"
)

echo.
echo ========================================
echo 配置创建完成！
echo ========================================
echo.
echo 已创建以下文件:
echo.

for /L %%i in (1,1,%PROJECT_COUNT%) do (
    echo - configs\%BASE_NAME%-config-%%i.json
    echo - configs\start-%BASE_NAME%-%%i.bat
)

echo.
echo 使用说明:
echo 1. 将对应的 JSON 配置文件内容复制到您的 MCP 客户端配置中
echo 2. 或者直接使用对应的启动脚本来启动特定实例
echo 3. 每个配置都有独立的服务器名称和实例ID，避免冲突
echo.

REM 创建合并的配置文件
echo 正在创建合并的配置文件...
(
    echo {
    echo   "mcpServers": {
    for /L %%i in (1,1,%PROJECT_COUNT%) do (
        echo     "%BASE_NAME%-%%i": {
        echo       "command": "java",
        echo       "args": [
        echo         "-Dspring.ai.mcp.server.transport=STDIO",
        echo         "-Dspring.main.web-application-type=none",
        echo         "-Dlogging.pattern.console=",
        echo         "-DMCP_SERVER_NAME=%BASE_NAME%-%%i",
        echo         "-DMCP_INSTANCE_ID=%BASE_NAME%-instance-%%i",
        echo         "-Dserver.port=0",
        echo         "-Dspring.jmx.enabled=false",
        echo         "-jar",
        echo         "%CD:\=\\%\\target\\qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
        echo       ],
        echo       "env": {
        echo         "CONFLUENCE_USERNAME": "%%CONFLUENCE_USERNAME%%",
        echo         "CONFLUENCE_PASSWORD": "%%CONFLUENCE_PASSWORD%%"
        echo       }
        if %%i LSS %PROJECT_COUNT% (
            echo     },
        ) else (
            echo     }
        )
    )
    echo   }
    echo }
) > "configs\%BASE_NAME%-all-configs.json"

echo.
echo 合并配置文件已创建: configs\%BASE_NAME%-all-configs.json
echo 您可以将此文件的内容直接用于支持多个MCP服务器的客户端
echo.

pause
