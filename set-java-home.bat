@echo off
REM Set JAVA_HOME and build project

echo Setting JAVA_HOME and building project...
echo.

REM Try to find Java installation
set JAVA_FOUND=0

if exist "C:\Program Files\Java\jdk-17.0.1" (
    set JAVA_HOME=C:\Program Files\Java\jdk-17.0.1
    set JAVA_FOUND=1
    echo Found Java at: C:\Program Files\Java\jdk-17.0.1
) else if exist "C:\Program Files\Java\jdk-17" (
    set JAVA_HOME=C:\Program Files\Java\jdk-17
    set JAVA_FOUND=1
    echo Found Java at: C:\Program Files\Java\jdk-17
) else if exist "C:\Program Files\Java\jdk-21" (
    set JAVA_HOME=C:\Program Files\Java\jdk-21
    set JAVA_FOUND=1
    echo Found Java at: C:\Program Files\Java\jdk-21
) else if exist "C:\Program Files\OpenJDK\jdk-17" (
    set JAVA_HOME=C:\Program Files\OpenJDK\jdk-17
    set JAVA_FOUND=1
    echo Found Java at: C:\Program Files\OpenJDK\jdk-17
) else if exist "C:\Program Files\Eclipse Adoptium\jdk-17" (
    set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-17
    set JAVA_FOUND=1
    echo Found Java at: C:\Program Files\Eclipse Adoptium\jdk-17
)

if %JAVA_FOUND%==0 (
    echo Could not find Java installation automatically
    echo Please set JAVA_HOME manually:
    echo.
    echo Example:
    echo set JAVA_HOME=C:\Program Files\Java\jdk-17
    echo.
    pause
    exit /b 1
)

echo JAVA_HOME set to: %JAVA_HOME%
echo.

REM Verify Java
if exist "%JAVA_HOME%\bin\java.exe" (
    echo Java executable found: %JAVA_HOME%\bin\java.exe
) else (
    echo ERROR: Java executable not found at %JAVA_HOME%\bin\java.exe
    pause
    exit /b 1
)

echo.
echo Building project...
echo.

REM Build with explicit JAVA_HOME
call mvnw.cmd clean package -DskipTests

if %ERRORLEVEL% neq 0 (
    echo.
    echo BUILD FAILED!
    pause
    exit /b 1
)

echo.
echo BUILD SUCCESS!
echo.

if exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo JAR file created successfully!
    echo Location: target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
    
    REM Show file size
    for %%I in (target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar) do echo Size: %%~zI bytes
    
    echo.
    echo Next steps:
    echo 1. Test the build: java -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar --help
    echo 2. Setup MCP configs: quick-multi-setup.bat
    echo 3. Start MCP server: start-mcp-server.bat
    
) else (
    echo WARNING: JAR file not created
)

echo.
pause
