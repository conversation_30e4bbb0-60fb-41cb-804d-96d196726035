@echo off
REM 快速多实例MCP配置脚本

echo ========================================
echo Confluence MCP 快速多实例配置
echo ========================================
echo.

echo 此脚本将快速为您创建多个MCP实例配置，解决同一包只能运行一次的问题。
echo.

REM 检查JAR文件是否存在
if not exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo 错误: 找不到JAR文件，请先构建项目
    echo 运行: mvnw clean install -DskipTests
    pause
    exit /b 1
)

REM 获取配置信息
set /p PROJECT_COUNT="请输入需要的项目数量 (1-10): "
if %PROJECT_COUNT% GTR 10 set PROJECT_COUNT=10
if %PROJECT_COUNT% LSS 1 set PROJECT_COUNT=1

echo.
echo 正在创建 %PROJECT_COUNT% 个MCP配置...
echo.

REM 创建输出目录
if not exist "multi-configs" mkdir "multi-configs"

REM 生成合并的MCP配置文件
(
    echo {
    echo   "mcpServers": {
    
    for /L %%i in (1,1,%PROJECT_COUNT%) do (
        if %%i GTR 1 echo     ,
        echo     "confluence-mcp-project%%i": {
        echo       "command": "java",
        echo       "args": [
        echo         "-Dspring.ai.mcp.server.transport=STDIO",
        echo         "-Dspring.main.web-application-type=none",
        echo         "-Dlogging.pattern.console=",
        echo         "-DMCP_SERVER_NAME=confluence-mcp-project%%i",
        echo         "-DMCP_INSTANCE_ID=project%%i-instance",
        echo         "-Dserver.port=0",
        echo         "-Dspring.jmx.enabled=false",
        echo         "-jar",
        echo         "%CD:\=\\%\\target\\qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
        echo       ],
        echo       "env": {
        echo         "CONFLUENCE_USERNAME": "%%CONFLUENCE_USERNAME%%",
        echo         "CONFLUENCE_PASSWORD": "%%CONFLUENCE_PASSWORD%%"
        echo       }
        echo     }
    )
    
    echo   }
    echo }
) > "multi-configs\mcp-multi-config.json"

REM 为每个项目创建独立的配置
for /L %%i in (1,1,%PROJECT_COUNT%) do (
    (
        echo {
        echo   "mcpServers": {
        echo     "confluence-mcp-project%%i": {
        echo       "command": "java",
        echo       "args": [
        echo         "-Dspring.ai.mcp.server.transport=STDIO",
        echo         "-Dspring.main.web-application-type=none",
        echo         "-Dlogging.pattern.console=",
        echo         "-DMCP_SERVER_NAME=confluence-mcp-project%%i",
        echo         "-DMCP_INSTANCE_ID=project%%i-instance",
        echo         "-Dserver.port=0",
        echo         "-Dspring.jmx.enabled=false",
        echo         "-jar",
        echo         "%CD:\=\\%\\target\\qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
        echo       ],
        echo       "env": {
        echo         "CONFLUENCE_USERNAME": "%%CONFLUENCE_USERNAME%%",
        echo         "CONFLUENCE_PASSWORD": "%%CONFLUENCE_PASSWORD%%"
        echo       }
        echo     }
        echo   }
        echo }
    ) > "multi-configs\mcp-project%%i-config.json"
    
    REM 创建独立启动脚本
    (
        echo @echo off
        echo REM Confluence MCP Project %%i 启动脚本
        echo.
        echo echo 启动 Confluence MCP Project %%i
        echo echo 服务器名称: confluence-mcp-project%%i
        echo echo 实例ID: project%%i-instance
        echo echo.
        echo.
        echo REM 设置环境变量 ^(请根据需要修改^)
        echo set CONFLUENCE_USERNAME=%%CONFLUENCE_USERNAME%%
        echo set CONFLUENCE_PASSWORD=%%CONFLUENCE_PASSWORD%%
        echo.
        echo REM 启动MCP服务器
        echo java -Dspring.ai.mcp.server.transport=STDIO ^^^
        echo      -Dspring.main.web-application-type=none ^^^
        echo      -Dlogging.pattern.console= ^^^
        echo      -DMCP_SERVER_NAME=confluence-mcp-project%%i ^^^
        echo      -DMCP_INSTANCE_ID=project%%i-instance ^^^
        echo      -Dserver.port=0 ^^^
        echo      -Dspring.jmx.enabled=false ^^^
        echo      -DCONFLUENCE_USERNAME=%%CONFLUENCE_USERNAME%% ^^^
        echo      -DCONFLUENCE_PASSWORD=%%CONFLUENCE_PASSWORD%% ^^^
        echo      -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
        echo.
        echo pause
    ) > "multi-configs\start-project%%i.bat"
)

REM 创建环境变量设置脚本
(
    echo @echo off
    echo REM 设置Confluence环境变量
    echo.
    echo echo 设置Confluence认证信息
    echo echo 这些环境变量将被所有MCP实例使用
    echo echo.
    echo.
    echo set /p CONFLUENCE_USERNAME="请输入Confluence用户名: "
    echo set /p CONFLUENCE_PASSWORD="请输入Confluence密码或API Token: "
    echo.
    echo echo.
    echo echo 设置环境变量...
    echo setx CONFLUENCE_USERNAME "%%CONFLUENCE_USERNAME%%"
    echo setx CONFLUENCE_PASSWORD "%%CONFLUENCE_PASSWORD%%"
    echo.
    echo echo 环境变量已设置，请重启命令行窗口使其生效
    echo pause
) > "multi-configs\set-env.bat"

REM 创建测试脚本
(
    echo @echo off
    echo REM 测试所有MCP实例
    echo.
    echo echo 测试MCP实例连接...
    echo echo.
    echo.
    for /L %%i in (1,1,%PROJECT_COUNT%) do (
        echo echo 测试 Project %%i...
        echo call start-project%%i.bat
        echo timeout /t 2 /nobreak ^> nul
        echo echo.
    )
    echo.
    echo echo 所有实例测试完成
    echo pause
) > "multi-configs\test-all.bat"

echo ========================================
echo 配置创建完成！
echo ========================================
echo.

echo 已创建以下文件:
echo.
echo 主配置文件:
echo   - multi-configs\mcp-multi-config.json     ^(包含所有实例的合并配置^)
echo.
echo 独立配置文件:
for /L %%i in (1,1,%PROJECT_COUNT%) do (
    echo   - multi-configs\mcp-project%%i-config.json
)
echo.
echo 启动脚本:
for /L %%i in (1,1,%PROJECT_COUNT%) do (
    echo   - multi-configs\start-project%%i.bat
)
echo.
echo 辅助脚本:
echo   - multi-configs\set-env.bat               ^(设置环境变量^)
echo   - multi-configs\test-all.bat              ^(测试所有实例^)
echo.

echo ========================================
echo 使用说明
echo ========================================
echo.
echo 1. 首先设置环境变量:
echo    cd multi-configs
echo    set-env.bat
echo.
echo 2. 选择使用方式:
echo.
echo    方式A - 使用合并配置 ^(推荐^):
echo    将 mcp-multi-config.json 的内容复制到您的MCP客户端配置中
echo.
echo    方式B - 使用独立配置:
echo    为每个项目单独使用对应的 mcp-projectX-config.json
echo.
echo    方式C - 直接启动:
echo    运行对应的 start-projectX.bat 脚本
echo.
echo 3. 测试连接:
echo    test-all.bat
echo.

echo 现在您可以同时运行多个MCP实例，每个服务不同的项目！
echo.

pause
