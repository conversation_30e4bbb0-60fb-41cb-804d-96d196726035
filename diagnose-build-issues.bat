@echo off
REM 构建问题诊断脚本

echo ========================================
echo Confluence MCP 构建问题诊断
echo ========================================
echo.

echo 1. 检查Java环境...
echo ----------------------------------------

REM 检查Java命令
java -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ✗ Java命令不可用
    echo   请安装Java 17或更高版本
) else (
    echo ✓ Java命令可用
    java -version 2>&1 | findstr "version"
)

REM 检查JAVA_HOME
if "%JAVA_HOME%"=="" (
    echo ✗ JAVA_HOME环境变量未设置
) else (
    echo ✓ JAVA_HOME已设置: %JAVA_HOME%
    if exist "%JAVA_HOME%\bin\java.exe" (
        echo ✓ JAVA_HOME路径有效
    ) else (
        echo ✗ JAVA_HOME路径无效
    )
)

echo.
echo 2. 检查Maven环境...
echo ----------------------------------------

REM 检查Maven Wrapper
if exist "mvnw.cmd" (
    echo ✓ Maven Wrapper存在
) else (
    echo ✗ Maven Wrapper不存在
)

REM 检查系统Maven
mvn -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ✗ 系统Maven不可用
) else (
    echo ✓ 系统Maven可用
    mvn -version 2>&1 | findstr "Apache Maven"
)

echo.
echo 3. 检查项目文件...
echo ----------------------------------------

REM 检查pom.xml
if exist "pom.xml" (
    echo ✓ pom.xml存在
    
    REM 检查pom.xml语法
    findstr "<n>" pom.xml >nul
    if %ERRORLEVEL% equ 0 (
        echo ✗ pom.xml包含错误的标签 ^<n^>
        echo   应该是 ^<name^>
    ) else (
        echo ✓ pom.xml标签正确
    )
    
    REM 检查关键依赖
    findstr "spring-ai-starter-mcp-server" pom.xml >nul
    if %ERRORLEVEL% equ 0 (
        echo ✓ Spring AI MCP依赖存在
    ) else (
        echo ✗ 缺少Spring AI MCP依赖
    )
    
) else (
    echo ✗ pom.xml不存在
)

REM 检查源代码文件
if exist "src\main\java\com\qccconfluencemcp\ConfluenceMcpApplication.java" (
    echo ✓ 主应用类存在
) else (
    echo ✗ 主应用类不存在
)

if exist "src\main\java\com\qccconfluencemcp\ConfluenceService.java" (
    echo ✓ Confluence服务类存在
) else (
    echo ✗ Confluence服务类不存在
)

if exist "src\main\resources\application.yml" (
    echo ✓ 配置文件存在
) else (
    echo ✗ 配置文件不存在
)

echo.
echo 4. 检查网络连接...
echo ----------------------------------------

REM 检查Maven中央仓库连接
ping -n 1 repo1.maven.org >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✓ 可以连接到Maven中央仓库
) else (
    echo ✗ 无法连接到Maven中央仓库
    echo   请检查网络连接和防火墙设置
)

echo.
echo 5. 检查目标目录...
echo ----------------------------------------

if exist "target" (
    echo ✓ target目录存在
    
    if exist "target\classes" (
        echo ✓ 编译输出目录存在
    ) else (
        echo ✗ 编译输出目录不存在
    )
    
    if exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
        echo ✓ JAR文件已存在
        for %%I in (target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar) do echo   文件大小: %%~zI 字节
    ) else (
        echo ✗ JAR文件不存在
    )
    
) else (
    echo ✗ target目录不存在
)

echo.
echo 6. 建议的解决方案...
echo ----------------------------------------

if "%JAVA_HOME%"=="" (
    echo • 设置JAVA_HOME环境变量
    echo   示例: set JAVA_HOME=C:\Program Files\Java\jdk-17
    echo.
)

java -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo • 安装Java 17或更高版本
    echo   下载地址: https://adoptium.net/
    echo.
)

if not exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo • 运行构建脚本: build-project.bat
    echo • 或手动构建: mvnw.cmd clean package -DskipTests
    echo.
)

findstr "<n>" pom.xml >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo • 修复pom.xml中的错误标签
    echo   运行: fix-pom.bat
    echo.
)

echo ========================================
echo 诊断完成
echo ========================================

pause
