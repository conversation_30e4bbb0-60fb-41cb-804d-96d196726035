@echo off
REM 多实例MCP服务器管理脚本

setlocal enabledelayedexpansion

echo ========================================
echo MCP多实例管理器
echo ========================================
echo.

:MAIN_MENU
echo 请选择操作:
echo 1. 创建新的MCP实例配置
echo 2. 启动指定实例
echo 3. 查看所有实例状态
echo 4. 停止所有实例
echo 5. 生成客户端配置
echo 6. 退出
echo.
set /p CHOICE="请输入选择 (1-6): "

if "%CHOICE%"=="1" goto CREATE_INSTANCE
if "%CHOICE%"=="2" goto START_INSTANCE
if "%CHOICE%"=="3" goto LIST_INSTANCES
if "%CHOICE%"=="4" goto STOP_INSTANCES
if "%CHOICE%"=="5" goto GENERATE_CONFIG
if "%CHOICE%"=="6" goto EXIT
goto MAIN_MENU

:CREATE_INSTANCE
echo.
echo ========================================
echo 创建新的MCP实例配置
echo ========================================
echo.

set /p INSTANCE_NAME="请输入实例名称: "
set /p CONFLUENCE_URL="请输入Confluence服务器地址 (默认: https://doc.greatld.com): "
if "%CONFLUENCE_URL%"=="" set CONFLUENCE_URL=https://doc.greatld.com

set /p USERNAME="请输入Confluence用户名: "
set /p PASSWORD="请输入Confluence密码或API Token: "

REM 创建实例目录
if not exist "instances" mkdir instances
if not exist "instances\%INSTANCE_NAME%" mkdir "instances\%INSTANCE_NAME%"

REM 创建实例特定的配置文件
(
    echo # %INSTANCE_NAME% 实例配置
    echo spring:
    echo   application:
    echo     name: confluence-mcp-%INSTANCE_NAME%
    echo   main:
    echo     banner-mode: off
    echo     web-application-type: none
    echo   ai:
    echo     mcp:
    echo       server:
    echo         enabled: true
    echo         name: confluence-mcp-%INSTANCE_NAME%
    echo         version: 1.0.0
    echo         type: SYNC
    echo         instance-id: %INSTANCE_NAME%-!random!
    echo.
    echo server:
    echo   port: 0
    echo.
    echo spring:
    echo   jmx:
    echo     enabled: false
    echo.
    echo confluence:
    echo   base-url: %CONFLUENCE_URL%
    echo   username: %USERNAME%
    echo   password: %PASSWORD%
    echo   connect-timeout: 30000
    echo   read-timeout: 60000
    echo   ssl-verification: true
    echo.
    echo logging:
    echo   level:
    echo     com.qccconfluencemcp: INFO
    echo   pattern:
    echo     console: ""
    echo   file:
    echo     name: ./target/confluence-mcp-%INSTANCE_NAME%.log
) > "instances\%INSTANCE_NAME%\application-%INSTANCE_NAME%.yml"

REM 创建启动脚本
(
    echo @echo off
    echo REM %INSTANCE_NAME% MCP实例启动脚本
    echo.
    echo echo 启动MCP实例: %INSTANCE_NAME%
    echo echo 配置文件: application-%INSTANCE_NAME%.yml
    echo echo.
    echo.
    echo java -Dspring.profiles.active=%INSTANCE_NAME% ^^^
    echo      -Dspring.config.location=instances\%INSTANCE_NAME%\application-%INSTANCE_NAME%.yml ^^^
    echo      -Dspring.ai.mcp.server.transport=STDIO ^^^
    echo      -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
) > "instances\%INSTANCE_NAME%\start-%INSTANCE_NAME%.bat"

REM 创建MCP客户端配置
(
    echo {
    echo   "mcpServers": {
    echo     "confluence-mcp-%INSTANCE_NAME%": {
    echo       "command": "java",
    echo       "args": [
    echo         "-Dspring.profiles.active=%INSTANCE_NAME%",
    echo         "-Dspring.config.location=instances\\%INSTANCE_NAME%\\application-%INSTANCE_NAME%.yml",
    echo         "-Dspring.ai.mcp.server.transport=STDIO",
    echo         "-jar",
    echo         "%CD:\=\\%\\target\\qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
    echo       ]
    echo     }
    echo   }
    echo }
) > "instances\%INSTANCE_NAME%\mcp-config-%INSTANCE_NAME%.json"

echo.
echo 实例 "%INSTANCE_NAME%" 创建成功！
echo.
echo 创建的文件:
echo - instances\%INSTANCE_NAME%\application-%INSTANCE_NAME%.yml
echo - instances\%INSTANCE_NAME%\start-%INSTANCE_NAME%.bat
echo - instances\%INSTANCE_NAME%\mcp-config-%INSTANCE_NAME%.json
echo.
pause
goto MAIN_MENU

:START_INSTANCE
echo.
echo ========================================
echo 启动MCP实例
echo ========================================
echo.

if not exist "instances" (
    echo 没有找到实例目录，请先创建实例。
    pause
    goto MAIN_MENU
)

echo 可用的实例:
for /d %%i in (instances\*) do (
    echo - %%~ni
)
echo.

set /p INSTANCE_NAME="请输入要启动的实例名称: "

if not exist "instances\%INSTANCE_NAME%" (
    echo 实例 "%INSTANCE_NAME%" 不存在。
    pause
    goto MAIN_MENU
)

echo.
echo 启动实例: %INSTANCE_NAME%
echo.

call "instances\%INSTANCE_NAME%\start-%INSTANCE_NAME%.bat"

pause
goto MAIN_MENU

:LIST_INSTANCES
echo.
echo ========================================
echo 实例列表
echo ========================================
echo.

if not exist "instances" (
    echo 没有找到实例目录。
    pause
    goto MAIN_MENU
)

echo 已创建的实例:
for /d %%i in (instances\*) do (
    echo.
    echo 实例名称: %%~ni
    if exist "instances\%%~ni\application-%%~ni.yml" (
        echo   配置文件: ✓
    ) else (
        echo   配置文件: ✗
    )
    if exist "instances\%%~ni\start-%%~ni.bat" (
        echo   启动脚本: ✓
    ) else (
        echo   启动脚本: ✗
    )
    if exist "instances\%%~ni\mcp-config-%%~ni.json" (
        echo   MCP配置: ✓
    ) else (
        echo   MCP配置: ✗
    )
)

echo.
pause
goto MAIN_MENU

:STOP_INSTANCES
echo.
echo ========================================
echo 停止所有实例
echo ========================================
echo.

echo 正在停止所有Java MCP进程...
taskkill /f /im java.exe 2>nul
echo 完成。
echo.
pause
goto MAIN_MENU

:GENERATE_CONFIG
echo.
echo ========================================
echo 生成客户端配置
echo ========================================
echo.

if not exist "instances" (
    echo 没有找到实例目录。
    pause
    goto MAIN_MENU
)

echo 正在生成合并的MCP客户端配置...

(
    echo {
    echo   "mcpServers": {
    
    set FIRST=1
    for /d %%i in (instances\*) do (
        if !FIRST! NEQ 1 echo     ,
        echo     "confluence-mcp-%%~ni": {
        echo       "command": "java",
        echo       "args": [
        echo         "-Dspring.profiles.active=%%~ni",
        echo         "-Dspring.config.location=instances\\%%~ni\\application-%%~ni.yml",
        echo         "-Dspring.ai.mcp.server.transport=STDIO",
        echo         "-jar",
        echo         "%CD:\=\\%\\target\\qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
        echo       ]
        echo     }
        set FIRST=0
    )
    
    echo   }
    echo }
) > "mcp-all-instances-config.json"

echo.
echo 合并配置已生成: mcp-all-instances-config.json
echo.
echo 配置内容:
echo ----------------------------------------
type "mcp-all-instances-config.json"
echo ----------------------------------------
echo.
pause
goto MAIN_MENU

:EXIT
echo.
echo 退出管理器。
exit /b 0
