@echo off
REM Augment MCP配置设置脚本

echo ========================================
echo Augment MCP 配置设置向导
echo ========================================
echo.

REM 检查JAR文件是否存在
if not exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo 错误: JAR文件不存在，请先构建项目
    echo 运行命令: mvn clean install -DskipTests
    pause
    exit /b 1
)

echo JAR文件检查通过 ✓
echo.

REM 获取用户输入
set /p CONFLUENCE_USERNAME="请输入Confluence用户名: "
set /p CONFLUENCE_PASSWORD="请输入Confluence密码或API Token: "

echo.
echo 正在生成Augment配置文件...

REM 获取当前目录的绝对路径
set CURRENT_DIR=%CD%
set JAR_PATH=%CURRENT_DIR%\target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar

REM 生成配置文件
(
echo {
echo   "mcpServers": {
echo     "qccConfluenceMcp": {
echo       "command": "java",
echo       "args": [
echo         "-Dspring.ai.mcp.server.transport=STDIO",
echo         "-Dspring.main.web-application-type=none",
echo         "-Dlogging.pattern.console=",
echo         "-jar",
echo         "%JAR_PATH:\=\\%"
echo       ],
echo       "env": {
echo         "CONFLUENCE_USERNAME": "%CONFLUENCE_USERNAME%",
echo         "CONFLUENCE_PASSWORD": "%CONFLUENCE_PASSWORD%"
echo       }
echo     }
echo   }
echo }
) > augment-mcp-config-generated.json

echo.
echo 配置文件已生成: augment-mcp-config-generated.json
echo.
echo 请将此文件的内容复制到Augment的MCP服务器配置中。
echo.
echo 配置内容:
echo ----------------------------------------
type augment-mcp-config-generated.json
echo ----------------------------------------
echo.

REM 测试连接
echo 是否要测试MCP服务器连接? (y/n)
set /p TEST_CONNECTION=
if /i "%TEST_CONNECTION%"=="y" (
    echo.
    echo 正在测试MCP服务器...
    java -Dspring.ai.mcp.server.transport=STDIO ^
         -Dspring.main.web-application-type=none ^
         -Dlogging.pattern.console= ^
         -DCONFLUENCE_USERNAME=%CONFLUENCE_USERNAME% ^
         -DCONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD% ^
         -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
)

echo.
echo 配置完成！
echo 请查看 AUGMENT_CONFIG_GUIDE.md 获取详细说明。

pause
