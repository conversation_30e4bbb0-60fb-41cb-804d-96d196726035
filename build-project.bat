@echo off
REM 项目构建脚本 - 解决JAVA_HOME和Maven问题

echo ========================================
echo Confluence MCP 项目构建
echo ========================================
echo.

REM 检查Java是否可用
java -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误: 找不到Java命令
    echo 请确保Java已安装并在PATH中
    echo.
    echo 如果Java已安装，请设置JAVA_HOME环境变量:
    echo set JAVA_HOME=C:\Program Files\Java\jdk-17
    echo.
    pause
    exit /b 1
)

echo Java版本信息:
java -version
echo.

REM 尝试查找Java安装路径
for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION=%%g
)

REM 如果JAVA_HOME未设置，尝试自动设置
if "%JAVA_HOME%"=="" (
    echo JAVA_HOME未设置，尝试自动检测...
    
    REM 常见的Java安装路径
    set POSSIBLE_PATHS=C:\Program Files\Java\jdk-17;C:\Program Files\Java\jdk-21;C:\Program Files\Java\jdk-11;C:\Program Files\OpenJDK\jdk-17;C:\Program Files\Eclipse Adoptium\jdk-17
    
    for %%p in (%POSSIBLE_PATHS%) do (
        if exist "%%p\bin\java.exe" (
            set JAVA_HOME=%%p
            echo 找到Java安装路径: %%p
            goto :java_found
        )
    )
    
    echo 无法自动检测JAVA_HOME，请手动设置
    echo 示例: set JAVA_HOME=C:\Program Files\Java\jdk-17
    pause
    exit /b 1
)

:java_found
echo JAVA_HOME: %JAVA_HOME%
echo.

REM 检查Maven Wrapper是否存在
if exist "mvnw.cmd" (
    echo 使用Maven Wrapper构建项目...
    echo.
    
    REM 设置JAVA_HOME并运行Maven
    set JAVA_HOME=%JAVA_HOME%
    call mvnw.cmd clean compile -DskipTests
    
    if %ERRORLEVEL% neq 0 (
        echo.
        echo 编译失败！请检查错误信息。
        echo.
        echo 常见问题解决方案:
        echo 1. 确保网络连接正常（Maven需要下载依赖）
        echo 2. 检查Java版本是否为17或更高
        echo 3. 清理Maven缓存: mvnw.cmd dependency:purge-local-repository
        echo.
        pause
        exit /b 1
    )
    
    echo.
    echo 编译成功！现在打包项目...
    echo.
    
    call mvnw.cmd package -DskipTests
    
    if %ERRORLEVEL% neq 0 (
        echo.
        echo 打包失败！
        pause
        exit /b 1
    )
    
) else (
    echo Maven Wrapper不存在，尝试使用系统Maven...
    
    mvn -version >nul 2>&1
    if %ERRORLEVEL% neq 0 (
        echo 错误: 找不到Maven命令
        echo 请安装Maven或使用Maven Wrapper
        pause
        exit /b 1
    )
    
    echo 使用系统Maven构建项目...
    mvn clean compile package -DskipTests
    
    if %ERRORLEVEL% neq 0 (
        echo 构建失败！
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo 构建完成！
echo ========================================
echo.

REM 检查JAR文件是否生成
if exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo ✓ JAR文件已生成: target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
    
    REM 显示文件大小
    for %%I in (target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar) do echo   文件大小: %%~zI 字节
    
    echo.
    echo 现在您可以:
    echo 1. 运行MCP服务器: start-mcp-server.bat
    echo 2. 配置多实例: quick-multi-setup.bat
    echo 3. 测试连接: test-mcp-client.bat
    
) else (
    echo ✗ JAR文件未生成，构建可能失败
)

echo.
pause
