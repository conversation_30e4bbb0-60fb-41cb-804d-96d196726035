# MCP多实例运行解决方案

## 问题描述

您遇到的问题是**同一个MCP包只能运行一次配置，本地多个项目配置只有一个可以运行**。

## 问题原因分析

### 1. 资源冲突
- **端口冲突**: 虽然MCP使用STDIO，但Spring Boot可能仍会尝试绑定默认端口
- **JMX冲突**: 多个Spring Boot实例的JMX端口冲突
- **应用名称冲突**: 相同的应用名称导致Spring上下文冲突

### 2. 配置冲突
- **服务器名称重复**: MCP服务器使用相同的名称
- **实例ID冲突**: 没有唯一的实例标识符
- **日志文件冲突**: 多个实例写入同一个日志文件

### 3. 进程管理问题
- **资源竞争**: 多个进程访问相同的资源
- **配置文件共享**: 使用相同的配置文件

## 解决方案

### 方案1: 使用多实例管理器（推荐）

1. **运行多实例管理器**:
   ```bash
   manage-multiple-instances.bat
   ```

2. **创建独立实例**:
   - 选择选项1创建新实例
   - 为每个项目创建独立的配置
   - 每个实例有唯一的名称和配置

3. **启动特定实例**:
   - 选择选项2启动指定实例
   - 每个实例独立运行，不会冲突

### 方案2: 手动创建多个配置

1. **运行配置生成器**:
   ```bash
   create-multiple-configs.bat
   ```

2. **指定项目数量**:
   - 输入需要的配置数量
   - 系统自动生成独立配置

3. **使用生成的配置**:
   - 每个配置有独立的服务器名称
   - 避免了资源冲突

### 方案3: 环境变量区分

为每个项目设置不同的环境变量：

```bash
# 项目1
set MCP_SERVER_NAME=confluence-mcp-project1
set MCP_INSTANCE_ID=project1-instance
set CONFLUENCE_USERNAME=user1
set CONFLUENCE_PASSWORD=pass1

# 项目2  
set MCP_SERVER_NAME=confluence-mcp-project2
set MCP_INSTANCE_ID=project2-instance
set CONFLUENCE_USERNAME=user2
set CONFLUENCE_PASSWORD=pass2
```

## 配置示例

### 多实例MCP配置

```json
{
  "mcpServers": {
    "confluence-mcp-project1": {
      "command": "java",
      "args": [
        "-Dspring.ai.mcp.server.transport=STDIO",
        "-Dspring.main.web-application-type=none",
        "-Dlogging.pattern.console=",
        "-DMCP_SERVER_NAME=confluence-mcp-project1",
        "-DMCP_INSTANCE_ID=project1-instance",
        "-Dserver.port=0",
        "-Dspring.jmx.enabled=false",
        "-jar",
        "C:/path/to/qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
      ],
      "env": {
        "CONFLUENCE_USERNAME": "user1",
        "CONFLUENCE_PASSWORD": "pass1"
      }
    },
    "confluence-mcp-project2": {
      "command": "java",
      "args": [
        "-Dspring.ai.mcp.server.transport=STDIO",
        "-Dspring.main.web-application-type=none",
        "-Dlogging.pattern.console=",
        "-DMCP_SERVER_NAME=confluence-mcp-project2", 
        "-DMCP_INSTANCE_ID=project2-instance",
        "-Dserver.port=0",
        "-Dspring.jmx.enabled=false",
        "-jar",
        "C:/path/to/qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
      ],
      "env": {
        "CONFLUENCE_USERNAME": "user2",
        "CONFLUENCE_PASSWORD": "pass2"
      }
    }
  }
}
```

## 关键改进点

### 1. 应用配置改进
- 添加了`MCP_SERVER_NAME`和`MCP_INSTANCE_ID`环境变量支持
- 设置`server.port=0`使用随机端口
- 禁用JMX避免冲突
- 独立的日志文件命名

### 2. 启动参数优化
- `-Dserver.port=0`: 随机端口分配
- `-Dspring.jmx.enabled=false`: 禁用JMX
- `-DMCP_SERVER_NAME`: 唯一服务器名称
- `-DMCP_INSTANCE_ID`: 唯一实例标识

### 3. 资源隔离
- 每个实例使用独立的配置文件
- 独立的日志文件
- 独立的环境变量

## 使用步骤

### 快速开始

1. **构建项目**:
   ```bash
   ./mvnw clean install -DskipTests
   ```

2. **运行多实例管理器**:
   ```bash
   manage-multiple-instances.bat
   ```

3. **创建实例**:
   - 选择选项1
   - 输入实例名称（如：project1, project2）
   - 输入对应的Confluence配置

4. **生成客户端配置**:
   - 选择选项5
   - 复制生成的配置到您的MCP客户端

5. **启动实例**:
   - 选择选项2
   - 选择要启动的实例

### 验证多实例运行

1. **检查进程**:
   ```bash
   tasklist | findstr java
   ```

2. **检查日志**:
   ```bash
   dir target\confluence-mcp-*.log
   ```

3. **测试连接**:
   每个实例都应该能够独立响应MCP请求

## 故障排除

### 常见问题

1. **端口仍然冲突**:
   - 确保使用了`-Dserver.port=0`参数
   - 检查是否有其他Spring Boot应用占用端口

2. **JMX冲突**:
   - 确保使用了`-Dspring.jmx.enabled=false`参数

3. **配置文件冲突**:
   - 确保每个实例使用独立的配置文件
   - 检查`MCP_SERVER_NAME`和`MCP_INSTANCE_ID`是否唯一

4. **日志文件冲突**:
   - 检查日志文件名是否包含实例ID
   - 确保每个实例有独立的日志文件

### 调试技巧

1. **启用详细日志**:
   ```bash
   -Dlogging.level.com.qccconfluencemcp=DEBUG
   ```

2. **检查实例状态**:
   使用管理器的选项3查看所有实例状态

3. **清理进程**:
   使用管理器的选项4停止所有实例

## 总结

通过以上解决方案，您可以：

1. **同时运行多个MCP实例**，每个服务不同的项目
2. **避免资源冲突**，每个实例独立运行
3. **简化管理**，使用管理器脚本统一管理
4. **灵活配置**，每个实例可以有不同的Confluence配置

这样就解决了"同一个包只能运行一次配置"的问题，让您可以为多个项目同时提供MCP服务。
